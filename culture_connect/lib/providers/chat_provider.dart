import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/chat/chat_model.dart';
import 'package:culture_connect/models/message_model.dart' as msg;
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/services/translation_service.dart';
import 'package:culture_connect/services/offline_message_service.dart';

class ChatNotifier extends StateNotifier<AsyncValue<List<ChatModel>>> {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final TranslationService? _translationService;
  final OfflineMessageService? _offlineMessageService;
  final String _userId;

  ChatNotifier(this._firestore, this._storage, this._translationService,
      this._offlineMessageService, this._userId)
      : super(const AsyncValue.loading()) {
    _fetchChats();
  }

  /// Empty constructor for when user is not authenticated
  ChatNotifier.empty()
      : _firestore = FirebaseFirestore.instance,
        _storage = FirebaseStorage.instance,
        _translationService = null,
        _offlineMessageService = null,
        _userId = '',
        super(const AsyncValue.data([]));

  Future<void> _fetchChats() async {
    try {
      final snapshot = await _firestore
          .collection('chats')
          .where('participants', arrayContains: _userId)
          .orderBy('lastMessageAt', descending: true)
          .get();

      final chats = snapshot.docs.map((doc) {
        final data = doc.data();
        return ChatModel.fromJson({...data, 'id': doc.id});
      }).toList();

      state = AsyncValue.data(chats);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<String> createChat(String recipientId) async {
    try {
      // Check if chat already exists
      final existingChatQuery = await _firestore
          .collection('chats')
          .where('participants', arrayContains: _userId)
          .get();

      for (final doc in existingChatQuery.docs) {
        final chat = ChatModel.fromJson({...doc.data(), 'id': doc.id});
        if (chat.participants.contains(recipientId)) {
          return doc.id;
        }
      }

      // Create new chat
      final chatRef = _firestore.collection('chats').doc();
      final now = DateTime.now();

      await chatRef.set({
        'participants': [_userId, recipientId],
        'createdAt': now,
        'lastMessageAt': now,
        'lastMessageText': '',
        'lastMessageSenderId': '',
        'unreadCount': 0,
        'isActive': true,
      });

      _fetchChats();
      return chatRef.id;
    } catch (error) {
      rethrow;
    }
  }

  Future<void> sendMessage(msg.MessageModel message) async {
    // Return early if services are not available (empty constructor)
    if (_offlineMessageService == null) return;

    try {
      // Use offline message service to handle sending
      await _offlineMessageService!.sendMessage(message);

      // If we're online, update the chat info
      if (_offlineMessageService!.isOnline) {
        // Update chat with last message info
        await _firestore.collection('chats').doc(message.chatId).update({
          'lastMessageAt': message.timestamp,
          'lastMessageText': message.text,
          'lastMessageSenderId': message.senderId,
          'unreadCount': FieldValue.increment(1),
        });
      }

      _fetchChats();
    } catch (error) {
      rethrow;
    }
  }

  Future<void> sendMediaMessage(msg.MessageModel message, File file) async {
    // Return early if services are not available (empty constructor)
    if (_offlineMessageService == null) return;

    try {
      // Check if we're online
      if (_offlineMessageService!.isOnline) {
        // Upload file to Firebase Storage
        final storageRef = _storage
            .ref()
            .child('chat_media')
            .child(message.chatId)
            .child(
                '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}');

        final uploadTask = storageRef.putFile(file);
        final snapshot = await uploadTask.whenComplete(() {});
        final downloadUrl = await snapshot.ref.getDownloadURL();

        // Create updated message with media URL
        final updatedMessage = message.copyWith(
          mediaUrl: downloadUrl,
          status: msg.MessageStatus.sent,
        );

        // Save message locally and send to Firestore
        await _offlineMessageService!.sendMessage(updatedMessage);

        // Update chat with last message info
        await _firestore.collection('chats').doc(message.chatId).update({
          'lastMessageAt': message.timestamp,
          'lastMessageText': message.type == msg.MessageType.image
              ? '📷 Image'
              : message.type == msg.MessageType.video
                  ? '🎥 Video'
                  : message.type == msg.MessageType.audio
                      ? '🎵 Audio'
                      : 'File',
          'lastMessageSenderId': message.senderId,
          'unreadCount': FieldValue.increment(1),
        });
      } else {
        // We're offline, so we can't upload the file
        // Save the message with a local file path and pending status
        final pendingMessage = message.copyWith(
          mediaUrl: file.path, // Store local path
          status: msg.MessageStatus.pending,
        );

        // Save locally and add to pending queue
        await _offlineMessageService!.saveMessageLocally(pendingMessage);
        await _offlineMessageService!.addPendingMessage(pendingMessage);
      }

      _fetchChats();
    } catch (error) {
      // Save as pending message
      final pendingMessage = message.copyWith(
        status: msg.MessageStatus.pending,
      );
      await _offlineMessageService?.saveMessageLocally(pendingMessage);
      await _offlineMessageService?.addPendingMessage(pendingMessage);

      rethrow;
    }
  }

  Future<void> reportMessage(msg.MessageModel message) async {
    try {
      // Find the message document by matching fields
      final querySnapshot = await _firestore
          .collection('messages')
          .where('id', isEqualTo: message.id)
          .where('chatId', isEqualTo: message.chatId)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        throw Exception('Message not found');
      }

      final messageDoc = querySnapshot.docs.first;

      // Update the message status to reported
      await messageDoc.reference.update({
        'status': msg.MessageStatus.reported.toString().split('.').last,
      });

      // Add to reported messages collection for moderation
      await _firestore.collection('reported_messages').add({
        'messageId': message.id,
        'chatId': message.chatId,
        'reportedBy': _userId,
        'reportedAt': DateTime.now(),
        'status': 'pending',
      });
    } catch (error) {
      rethrow;
    }
  }

  Future<void> blockMessage(msg.MessageModel message) async {
    try {
      // Find the message document
      final querySnapshot = await _firestore
          .collection('messages')
          .where('id', isEqualTo: message.id)
          .where('chatId', isEqualTo: message.chatId)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        throw Exception('Message not found');
      }

      final messageDoc = querySnapshot.docs.first;

      // Update the message status to blocked
      await messageDoc.reference.update({
        'status': msg.MessageStatus.blocked.toString().split('.').last,
        'text': '[This message has been removed]',
      });
    } catch (error) {
      rethrow;
    }
  }

  /// Set typing status for the current user in a chat
  Future<void> setTypingStatus(String chatId, bool isTyping) async {
    try {
      final typingStatusRef =
          _firestore.collection('typing_status').doc(chatId);

      if (isTyping) {
        // Set typing status to true and add a timestamp
        await typingStatusRef.set({
          'typingUsers': {
            _userId: true,
          },
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        // Schedule automatic removal of typing status after 10 seconds of inactivity
        Future.delayed(const Duration(seconds: 10), () {
          clearTypingStatus(chatId);
        });
      } else {
        // Clear typing status
        await clearTypingStatus(chatId);
      }
    } catch (error) {
      // Silently fail - typing status is not critical
      debugPrint('Error setting typing status: $error');
    }
  }

  /// Clear typing status for the current user in a chat
  Future<void> clearTypingStatus(String chatId) async {
    try {
      final typingStatusRef =
          _firestore.collection('typing_status').doc(chatId);

      // Remove the current user from typing users
      await typingStatusRef.update({
        'typingUsers.$_userId': FieldValue.delete(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });
    } catch (error) {
      // Silently fail - typing status is not critical
      debugPrint('Error clearing typing status: $error');
    }
  }

  /// Add a reaction to a message
  Future<void> addReaction(
      msg.MessageModel message, msg.ReactionType reactionType) async {
    try {
      // Find the message document
      final querySnapshot = await _firestore
          .collection('messages')
          .where('id', isEqualTo: message.id)
          .where('chatId', isEqualTo: message.chatId)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        throw Exception('Message not found');
      }

      final messageDoc = querySnapshot.docs.first;

      // Add the reaction
      await messageDoc.reference.update({
        'reactions.$_userId.type': reactionType.toString().split('.').last,
        'reactions.$_userId.emoji': reactionType.emoji,
        'reactions.$_userId.timestamp': FieldValue.serverTimestamp(),
      });
    } catch (error) {
      rethrow;
    }
  }

  /// Remove a reaction from a message
  Future<void> removeReaction(msg.MessageModel message) async {
    try {
      // Find the message document
      final querySnapshot = await _firestore
          .collection('messages')
          .where('id', isEqualTo: message.id)
          .where('chatId', isEqualTo: message.chatId)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        throw Exception('Message not found');
      }

      final messageDoc = querySnapshot.docs.first;

      // Remove the reaction
      await messageDoc.reference.update({
        'reactions.$_userId': FieldValue.delete(),
      });
    } catch (error) {
      rethrow;
    }
  }

  Future<void> markMessagesAsRead(String chatId) async {
    try {
      // Get all unread messages in this chat
      final snapshot = await _firestore
          .collection('messages')
          .where('chatId', isEqualTo: chatId)
          .where('recipientId', isEqualTo: _userId)
          .where('status', isNotEqualTo: 'read')
          .get();

      // Update each message status to read
      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.update(doc.reference, {'status': 'read'});
      }
      await batch.commit();

      // Reset unread count for this chat
      await _firestore.collection('chats').doc(chatId).update({
        'unreadCount': 0,
      });

      _fetchChats();
    } catch (error) {
      rethrow;
    }
  }

  Future<void> translateMessage(
      msg.MessageModel message, String targetLanguage) async {
    try {
      if (message.type != msg.MessageType.text) return;

      // Check if translation already exists
      if (message.translations != null &&
          message.translations!.containsKey(targetLanguage)) {
        return;
      }

      // Return early if translation service is not available (empty constructor)
      if (_translationService == null) return;

      // Translate the message
      final translatedText = await _translationService!.translateText(
        message.text,
        message.originalLanguage,
        targetLanguage,
      );

      // Create updated translations map
      final translations = message.translations ?? {};
      translations[targetLanguage] = translatedText;

      // Update message in Firestore
      await _firestore
          .collection('messages')
          .where('id', isEqualTo: message.id)
          .get()
          .then((snapshot) {
        if (snapshot.docs.isNotEmpty) {
          snapshot.docs.first.reference.update({
            'isTranslated': true,
            'translations': translations,
          });
        }
      });
    } catch (error) {
      rethrow;
    }
  }

  Future<void> deleteMessage(String messageId) async {
    try {
      await _firestore
          .collection('messages')
          .where('id', isEqualTo: messageId)
          .get()
          .then((snapshot) {
        if (snapshot.docs.isNotEmpty) {
          snapshot.docs.first.reference.delete();
        }
      });
    } catch (error) {
      rethrow;
    }
  }

  Future<void> deleteChat(String chatId) async {
    try {
      // Delete all messages in the chat
      final messagesSnapshot = await _firestore
          .collection('messages')
          .where('chatId', isEqualTo: chatId)
          .get();

      final batch = _firestore.batch();
      for (final doc in messagesSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Delete the chat document
      batch.delete(_firestore.collection('chats').doc(chatId));
      await batch.commit();

      _fetchChats();
    } catch (error) {
      rethrow;
    }
  }
}

final chatProvider =
    StateNotifierProvider<ChatNotifier, AsyncValue<List<ChatModel>>>((ref) {
  final authState = ref.watch(authStateProvider);
  final user = authState.user;

  if (user == null) {
    // Return a notifier with empty data when user is not authenticated
    return ChatNotifier.empty();
  }

  final firestore = FirebaseFirestore.instance;
  final storage = FirebaseStorage.instance;
  final translationService = ref.watch(translationServiceProvider);
  final offlineMessageService = ref.watch(offlineMessageServiceProvider);

  return ChatNotifier(
      firestore, storage, translationService, offlineMessageService, user.id);
});

final chatMessagesProvider =
    StreamProvider.family<List<msg.MessageModel>, String>((ref, chatId) {
  final offlineMessageService = ref.watch(offlineMessageServiceProvider);
  final isOnline = offlineMessageService.isOnline;

  if (!isOnline) {
    // If offline, return messages from local storage
    final localMessages = offlineMessageService.getMessagesForChat(chatId);
    return Stream.value(localMessages);
  }

  // If online, merge local pending messages with Firestore messages
  return FirebaseFirestore.instance
      .collection('messages')
      .where('chatId', isEqualTo: chatId)
      .orderBy('timestamp', descending: false)
      .snapshots()
      .map((snapshot) {
    // Get messages from Firestore
    final firestoreMessages = snapshot.docs
        .map((doc) => msg.MessageModel.fromJson(doc.data()))
        .toList();

    // Get pending messages from local storage
    final pendingMessages = offlineMessageService
        .getPendingMessages()
        .where((msg) => msg.chatId == chatId)
        .toList();

    // Merge and sort by timestamp
    final allMessages = [...firestoreMessages, ...pendingMessages];
    allMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Save Firestore messages locally for offline access
    for (final message in firestoreMessages) {
      offlineMessageService.saveMessageLocally(message);
    }

    return allMessages;
  });
});

final userChatsProvider = StreamProvider<List<ChatModel>>((ref) {
  final authState = ref.watch(authStateProvider);
  final user = authState.user;

  if (user == null) {
    // Return empty stream when user is not authenticated
    return Stream.value(<ChatModel>[]);
  }

  return FirebaseFirestore.instance
      .collection('chats')
      .where('participants', arrayContains: user.id)
      .orderBy('lastMessageAt', descending: true)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => ChatModel.fromJson({...doc.data(), 'id': doc.id}))
          .toList());
});

final chatParticipantsProvider =
    FutureProvider.family<List<UserModel>, String>((ref, chatId) async {
  final chat =
      await FirebaseFirestore.instance.collection('chats').doc(chatId).get();

  if (!chat.exists) {
    return [];
  }

  final participants = List<String>.from(chat.data()?['participants'] ?? []);
  final currentUser = ref.watch(authStateProvider).user;

  if (currentUser == null) {
    return [];
  }

  // Remove current user from participants list
  participants.remove(currentUser.id);

  // Fetch user data for each participant
  final userDocs = await Future.wait(participants.map((userId) =>
      FirebaseFirestore.instance.collection('users').doc(userId).get()));

  return userDocs
      .where((doc) => doc.exists)
      .map((doc) => UserModel.fromJson({...doc.data()!, 'id': doc.id}))
      .toList();
});

/// Provider for typing status in a chat
final typingStatusProvider =
    StreamProvider.family<bool, Map<String, String>>((ref, params) {
  final chatId = params['chatId']!;
  final userId = params['userId']!;

  return FirebaseFirestore.instance
      .collection('typing_status')
      .doc(chatId)
      .snapshots()
      .map((snapshot) {
    if (!snapshot.exists) return false;
    final data = snapshot.data();
    if (data == null) return false;

    // Check if the other user is typing
    final typingUsers = data['typingUsers'] as Map<String, dynamic>? ?? {};
    return typingUsers.containsKey(userId) && typingUsers[userId] == true;
  });
});
