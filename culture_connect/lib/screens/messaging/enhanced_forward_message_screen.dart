// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/group_chat_model.dart';
import 'package:culture_connect/models/message_model.dart' as message_model;
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/chat_provider.dart';
import 'package:culture_connect/providers/group_chat_provider.dart';
import 'package:culture_connect/providers/user_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';

/// Enhanced screen for selecting recipients to forward messages to
/// with support for multiple messages and delivery confirmation
class EnhancedForwardMessageScreen extends ConsumerStatefulWidget {
  /// The messages to forward
  final List<message_model.MessageModel> messages;

  /// Creates a new forward message screen for a single message
  EnhancedForwardMessageScreen({
    super.key,
    required message_model.MessageModel message,
  }) : messages = [message];

  /// Creates a new forward message screen for multiple messages
  const EnhancedForwardMessageScreen.multiple({
    super.key,
    required this.messages,
  });

  @override
  ConsumerState<EnhancedForwardMessageScreen> createState() =>
      _EnhancedForwardMessageScreenState();
}

class _EnhancedForwardMessageScreenState
    extends ConsumerState<EnhancedForwardMessageScreen> {
  final List<String> _selectedChats = [];
  final List<String> _selectedGroups = [];
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // Track forwarding progress
  bool _isForwarding = false;
  int _forwardedCount = 0;
  int _totalToForward = 0;

  // Track delivery confirmations
  final Map<String, message_model.MessageStatus> _deliveryStatuses = {};

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text.toLowerCase();
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _toggleChatSelection(String chatId) {
    setState(() {
      if (_selectedChats.contains(chatId)) {
        _selectedChats.remove(chatId);
      } else {
        _selectedChats.add(chatId);
      }
    });
  }

  void _toggleGroupSelection(String groupId) {
    setState(() {
      if (_selectedGroups.contains(groupId)) {
        _selectedGroups.remove(groupId);
      } else {
        _selectedGroups.add(groupId);
      }
    });
  }

  Future<void> _forwardMessages() async {
    final currentUser = ref.read(currentUserModelProvider).value;
    if (currentUser == null) return;

    setState(() {
      _isForwarding = true;
      _totalToForward = widget.messages.length *
          (_selectedChats.length + _selectedGroups.length);
      _forwardedCount = 0;
    });

    try {
      // Create new messages based on the originals
      final now = DateTime.now();

      // Forward to selected chats
      for (final chatId in _selectedChats) {
        final chat = await ref.read(chatDetailsProvider(chatId).future);
        if (chat == null) continue;

        final recipientId = chat.participants.firstWhere(
          (id) => id != currentUser.id,
          orElse: () => '',
        );

        if (recipientId.isEmpty) continue;

        // Forward each message
        for (final originalMessage in widget.messages) {
          final messageId =
              '${now.millisecondsSinceEpoch}_${originalMessage.id}_$chatId';

          final forwardedMessage = message_model.MessageModel(
            id: messageId,
            chatId: chatId,
            senderId: currentUser.id,
            recipientId: recipientId,
            text: originalMessage.text,
            timestamp: now,
            status: message_model.MessageStatus.sending,
            type: originalMessage.type,
            mediaUrl: originalMessage.mediaUrl,
            isForwarded: true,
            originalSenderId: originalMessage.senderId,
          );

          try {
            await ref.read(chatProvider.notifier).sendMessage(forwardedMessage);

            // Track delivery status
            _deliveryStatuses[messageId] = message_model.MessageStatus.sent;

            setState(() {
              _forwardedCount++;
            });
          } catch (e) {
            // Record failure
            _deliveryStatuses[messageId] = message_model.MessageStatus.failed;
            debugPrint('Failed to forward message: $e');
          }
        }
      }

      // Forward to selected groups
      for (final groupId in _selectedGroups) {
        // Forward each message
        for (final originalMessage in widget.messages) {
          final messageId =
              '${now.millisecondsSinceEpoch}_${originalMessage.id}_$groupId';

          final forwardedMessage = message_model.MessageModel(
            id: messageId,
            chatId: groupId,
            senderId: currentUser.id,
            recipientId: '',
            text: originalMessage.text,
            timestamp: now,
            status: message_model.MessageStatus.sending,
            type: originalMessage.type,
            mediaUrl: originalMessage.mediaUrl,
            isForwarded: true,
            originalSenderId: originalMessage.senderId,
            isGroupMessage: true,
          );

          try {
            // For group chats, we'll use the chat provider
            await ref.read(chatProvider.notifier).sendMessage(forwardedMessage);

            // Track delivery status
            _deliveryStatuses[messageId] = message_model.MessageStatus.sent;

            setState(() {
              _forwardedCount++;
            });
          } catch (e) {
            // Record failure
            _deliveryStatuses[messageId] = message_model.MessageStatus.failed;
            debugPrint('Failed to forward message to group: $e');
          }
        }
      }
    } finally {
      setState(() {
        _isForwarding = false;
      });
    }

    if (mounted) {
      // Count successful forwards
      final successCount = _deliveryStatuses.values
          .where((status) => status == message_model.MessageStatus.sent)
          .length;

      final totalRecipients = _selectedChats.length + _selectedGroups.length;
      final totalMessages = widget.messages.length;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Forwarded $successCount of ${totalMessages * totalRecipients} messages to $totalRecipients recipients'),
          backgroundColor: successCount == totalMessages * totalRecipients
              ? Colors.green
              : Colors.orange,
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'DETAILS',
            onPressed: () {
              _showForwardingResultsDialog();
            },
          ),
        ),
      );

      Navigator.pop(context);
    }
  }

  void _showForwardingResultsDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Forwarding Results'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              Text(
                'Successfully forwarded: ${_deliveryStatuses.values.where((status) => status == message_model.MessageStatus.sent).length}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Failed: ${_deliveryStatuses.values.where((status) => status == message_model.MessageStatus.failed).length}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 16),
              const Text('Delivery Status:'),
              const SizedBox(height: 8),
              ..._deliveryStatuses.entries.map((entry) {
                final isSuccess =
                    entry.value == message_model.MessageStatus.sent;
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(
                        isSuccess ? Icons.check_circle : Icons.error,
                        color: isSuccess ? Colors.green : Colors.red,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          entry.key,
                          style: TextStyle(
                            color: isSuccess ? Colors.black87 : Colors.red,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CLOSE'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final chatsAsync = ref.watch(userChatsProvider);
    // Use the existing provider for group chats
    final groupsAsync = ref.watch(groupChatProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Forward Message${widget.messages.length > 1 ? 's' : ''}',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // Removed offline message status widget

          // Search bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),

          // Message preview
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.messages.length > 1
                      ? '${widget.messages.length} messages to forward:'
                      : 'Message to forward:',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                if (widget.messages.length == 1)
                  Text(
                    widget.messages[0].text,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppTheme.textPrimaryColor,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  )
                else
                  ...widget.messages.take(3).map((message) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Text(
                          '• ${message.text}',
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppTheme.textPrimaryColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )),
                if (widget.messages.length > 3)
                  Text(
                    '... and ${widget.messages.length - 3} more',
                    style: const TextStyle(
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
              ],
            ),
          ),

          // Chats and groups list
          Expanded(
            child: _isForwarding
                ? _buildForwardingProgress()
                : ListView(
                    children: [
                      // Chats section
                      const Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: Text(
                          'Chats',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      ),
                      chatsAsync.when(
                        data: (chats) {
                          final filteredChats = chats.where((chat) {
                            if (_searchQuery.isEmpty) return true;
                            return chat.lastMessageText
                                .toLowerCase()
                                .contains(_searchQuery);
                          }).toList();

                          return ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: filteredChats.length,
                            itemBuilder: (context, index) {
                              final chat = filteredChats[index];
                              return _buildChatTile(chat);
                            },
                          );
                        },
                        loading: () =>
                            const Center(child: CircularProgressIndicator()),
                        error: (error, stack) => Center(
                          child: Text('Error loading chats: $error'),
                        ),
                      ),

                      // Groups section
                      const Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: Text(
                          'Groups',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      ),
                      groupsAsync.when(
                        data: (groups) {
                          final filteredGroups = groups.where((group) {
                            if (_searchQuery.isEmpty) return true;
                            return group.name
                                .toLowerCase()
                                .contains(_searchQuery);
                          }).toList();

                          return ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: filteredGroups.length,
                            itemBuilder: (context, index) {
                              final group = filteredGroups[index];
                              return _buildGroupTile(group);
                            },
                          );
                        },
                        loading: () =>
                            const Center(child: CircularProgressIndicator()),
                        error: (error, stack) => Center(
                          child: Text('Error loading groups: $error'),
                        ),
                      ),
                    ],
                  ),
          ),
        ],
      ),
      floatingActionButton: _isForwarding
          ? null
          : FloatingActionButton.extended(
              onPressed: _selectedChats.isEmpty && _selectedGroups.isEmpty
                  ? null
                  : _forwardMessages,
              backgroundColor: _selectedChats.isEmpty && _selectedGroups.isEmpty
                  ? Colors.grey
                  : AppTheme.primaryColor,
              label: Text(
                'Forward (${_selectedChats.length + _selectedGroups.length})',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              icon: const Icon(Icons.send),
            ),
    );
  }

  Widget _buildForwardingProgress() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            value:
                _totalToForward > 0 ? _forwardedCount / _totalToForward : null,
          ),
          const SizedBox(height: 16),
          const Text(
            'Forwarding messages...',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          if (_totalToForward > 0)
            Text(
              '$_forwardedCount of $_totalToForward',
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildChatTile(dynamic chat) {
    final isSelected = _selectedChats.contains(chat.id);

    return FutureBuilder<UserModel?>(
      future: _getChatRecipient(chat),
      builder: (context, snapshot) {
        final recipient = snapshot.data;

        return ListTile(
          leading: CircleAvatar(
            backgroundColor: Colors.grey[300],
            backgroundImage: recipient?.profilePicture != null
                ? NetworkImage(recipient!.profilePicture!)
                : null,
            child: recipient?.profilePicture == null
                ? const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 24,
                  )
                : null,
          ),
          title: Text(
            recipient?.firstName ?? 'Unknown',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Text(
            chat.lastMessageText.isNotEmpty
                ? chat.lastMessageText
                : 'No messages yet',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          trailing: Checkbox(
            value: isSelected,
            onChanged: (value) => _toggleChatSelection(chat.id),
            activeColor: AppTheme.primaryColor,
          ),
          onTap: () => _toggleChatSelection(chat.id),
        );
      },
    );
  }

  Widget _buildGroupTile(GroupChatModel group) {
    final isSelected = _selectedGroups.contains(group.id);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Colors.grey[300],
        child: const Icon(
          Icons.group,
          color: Colors.white,
          size: 24,
        ),
      ),
      title: Text(
        group.name,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Text(
        '${group.members.length} members',
        style: const TextStyle(
          fontSize: 14,
          color: AppTheme.textSecondaryColor,
        ),
      ),
      trailing: Checkbox(
        value: isSelected,
        onChanged: (value) => _toggleGroupSelection(group.id),
        activeColor: AppTheme.primaryColor,
      ),
      onTap: () => _toggleGroupSelection(group.id),
    );
  }

  Future<UserModel?> _getChatRecipient(dynamic chat) async {
    final currentUser = ref.read(currentUserModelProvider).value;
    if (currentUser == null) return null;

    final recipientId = chat.participants.firstWhere(
      (id) => id != currentUser.id,
      orElse: () => '',
    );

    if (recipientId.isEmpty) return null;

    return ref.read(userProvider(recipientId).future);
  }
}
