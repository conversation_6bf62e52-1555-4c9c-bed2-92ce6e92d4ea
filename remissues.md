flutter: #181    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #182    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #183    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #184    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #185    _invoke (dart:ui/hooks.dart:312:13)
flutter: #186    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #187    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-01T01:10:42.768521] [FlutterError] A RenderFlex overflowed by 17 pixels on the bottom. A RenderFlex overflowed by 17 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #23     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #29     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #43     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #51     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #52     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #53     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #54     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #55     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #56     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #57     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #59     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #66     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #67     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #68     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #69     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #70     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #71     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #72     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #73     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #74     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #75     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #76     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #89     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #90     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #91     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #92     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #93     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #94     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #95     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #96     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #97     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #102    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #103    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #104    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #108    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #112    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #113    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #114    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #115    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #116    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #117    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #118    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #119    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #120    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #125    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #126    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #127    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #128    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #129    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #130    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #131    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #132    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #133    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #134    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #135    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #136    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #137    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #138    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #144    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #145    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #146    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #147    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #148    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #149    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #150    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #151    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #152    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #153    _invoke (dart:ui/hooks.dart:312:13)
flutter: #154    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #155    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-01T01:10:42.770403] [FlutterError] A RenderFlex overflowed by 15 pixels on the bottom. A RenderFlex overflowed by 15 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #23     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #29     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #43     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #51     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #52     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #53     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #54     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #55     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #56     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #57     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #59     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #66     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #67     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #68     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #69     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #70     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #71     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #72     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #73     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #74     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #75     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #76     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #89     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #90     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #91     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #92     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #93     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #94     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #95     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #96     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #97     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #102    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #103    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #104    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #108    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #112    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #113    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #114    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #115    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #116    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #117    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #118    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #119    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #120    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #125    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #126    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #127    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #128    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #129    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #130    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #131    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #132    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #133    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #134    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #135    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #136    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #137    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #138    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #144    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #145    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #146    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #147    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #148    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #149    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #150    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #151    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #152    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #153    _invoke (dart:ui/hooks.dart:312:13)
flutter: #154    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #155    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-01T01:10:42.772310] [FlutterError] A RenderFlex overflowed by 15 pixels on the bottom. A RenderFlex overflowed by 15 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #23     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #29     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #43     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #51     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #52     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #53     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #54     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #55     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #56     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #57     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #59     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #66     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #67     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #68     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #69     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #70     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #71     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #72     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #73     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #74     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #75     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #76     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #89     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #90     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #91     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #92     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #93     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #94     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #95     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #96     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #97     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #102    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #103    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #104    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #108    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #112    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #113    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #114    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #115    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #116    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #117    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #118    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #119    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #120    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #125    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #126    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #127    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #128    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #129    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #130    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #131    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #132    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #133    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #134    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #135    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #136    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #137    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #138    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #144    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #145    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #146    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #147    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #148    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #149    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #150    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #151    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #152    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #153    _invoke (dart:ui/hooks.dart:312:13)
flutter: #154    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #155    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-01T01:10:42.773729] [FlutterError] A RenderFlex overflowed by 15 pixels on the bottom. A RenderFlex overflowed by 15 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #23     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #29     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #43     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #51     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #52     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #53     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #54     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #55     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #56     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #57     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #59     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #66     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #67     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #68     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #69     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #70     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #71     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #72     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #73     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #74     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #75     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #76     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #89     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #90     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #91     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #92     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #93     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #94     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #95     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #96     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #97     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #102    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #103    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #104    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #108    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #112    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #113    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #114    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #115    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #116    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #117    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #118    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #119    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #120    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #125    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #126    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #127    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #128    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #129    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #130    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #131    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #132    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #133    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #134    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #135    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #136    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #137    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #138    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #144    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #145    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #146    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #147    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #148    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #149    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #150    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #151    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #152    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #153    _invoke (dart:ui/hooks.dart:312:13)
flutter: #154    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #155    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-01T01:10:42.775121] [FlutterError] A RenderFlex overflowed by 15 pixels on the bottom. A RenderFlex overflowed by 15 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #17     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #18     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #19     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #23     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #29     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #43     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #48     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #49     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #50     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #51     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #52     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #53     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #54     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #55     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #56     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #57     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #58     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #59     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #60     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #61     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #62     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #63     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #64     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #65     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #66     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #67     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #68     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #69     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #70     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #71     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #72     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #73     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #74     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #75     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #76     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #89     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #90     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #91     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #92     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #93     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #94     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #95     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #96     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #97     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #102    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #103    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #104    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #108    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #109    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #110    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #111    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #112    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #113    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #114    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #115    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #116    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #117    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #118    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #119    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #120    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #121    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #122    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #123    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #124    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #125    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #126    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #127    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #128    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #129    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #130    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #131    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #132    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #133    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #134    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #135    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #136    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #137    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #138    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #142    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #143    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #144    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #145    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #146    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #147    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #148    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #149    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #150    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #151    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #152    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #153    _invoke (dart:ui/hooks.dart:312:13)
flutter: #154    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #155    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-01T01:10:42.805187] [PerformanceMonitoringService] Slow frame detected {"duration_ms":127}
flutter: 🐛 DEBUG [2025-07-01T01:10:42.904310] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T01:10:43.041637] [ExperiencesNotifier] Loaded 6 experiences
flutter: Error getting user model: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: 🐛 DEBUG [2025-07-01T01:10:43.375388] [PerformanceMonitoringService] Slow frame detected {"duration_ms":333}
flutter: 🐛 DEBUG [2025-07-01T01:10:43.436349] [PerformanceMonitoringService] Slow frame detected {"duration_ms":65}
flutter: 🐛 DEBUG [2025-07-01T01:10:43.454943] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-07-01T01:10:43.531460] [PerformanceMonitoringService] Slow frame detected {"duration_ms":77}
flutter: 🐛 DEBUG [2025-07-01T01:10:43.554739] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-01T01:10:43.722800] [ExperiencesNotifier] Loaded 6 experiences
flutter: 🐛 DEBUG [2025-07-01T01:10:43.738608] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-01T01:10:43.871514] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-07-01T01:10:43.920935] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-01T01:10:43.971609] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-01T01:10:44.016722] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: ❌ ERROR [2025-07-01T01:10:44.050082] [FlutterError] dependOnInheritedWidgetOfExactType<_InheritedTheme>() or dependOnInheritedElement() was called before _AnimatedRefreshIndicatorState.initState() completed.
flutter: When an inherited widget changes, for example if the value of Theme.of() changes, its dependent widgets are rebuilt. If the dependent widget's reference to the inherited widget is in a constructor or an initState() method, then the rebuilt dependent widget will not reflect the changes in the inherited widget.
flutter: Typically references to inherited widgets should occur in widget build() methods. Alternatively, initialization based on inherited widgets can be placed in the didChangeDependencies method, which is called after initState and whenever the dependencies change thereafter. dependOnInheritedWidgetOfExactType<_InheritedTheme>() or dependOnInheritedElement() was called before _AnimatedRefreshIndicatorState.initState() completed.
flutter: When an inherited widget changes, for example if the value of Theme.of() changes, its dependent widgets are rebuilt. If the dependent widget's reference to the inherited widget is in a constructor or an initState() method, then the rebuilt dependent widget will not reflect the changes in the inherited widget.
flutter: Typically references to inherited widgets should occur in widget build() methods. Alternatively, initialization based on inherited widgets can be placed in the didChangeDependencies method, which is called after initState and whenever the dependencies change thereafter.
flutter: Stack trace:
flutter: #0      StatefulElement.dependOnInheritedElement.<anonymous closure> (package:flutter/src/widgets/framework.dart:5864:9)
flutter: #1      StatefulElement.dependOnInheritedElement (package:flutter/src/widgets/framework.dart:5907:6)
flutter: #2      Element.dependOnInheritedWidgetOfExactType (package:flutter/src/widgets/framework.dart:4922:14)
flutter: #3      Theme.of (package:flutter/src/material/theme.dart:119:53)
flutter: #4      _AnimatedRefreshIndicatorState._initializeAnimations (package:culture_connect/widgets/common/animated_refresh_indicator.dart:159:25)
flutter: #5      _AnimatedRefreshIndicatorState.initState (package:culture_connect/widgets/common/animated_refresh_indicator.dart:69:5)
flutter: #6      StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5762:55)
flutter: #7      ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #8      Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #9      Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #10     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #11     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #12     Element.updateChild (package:flutter/src/widgets/framework.dart:3957:20)
flutter: #13     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #14     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #15     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #16     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #17     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #18     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #19     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #20     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #21     StatelessElement.update (package:flutter/src/widgets/framework.dart:5707:5)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #25     StatelessElement.update (package:flutter/src/widgets/framework.dart:5707:5)
flutter: #26     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #27     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #28     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #29     StatelessElement.update (package:flutter/src/widgets/framework.dart:5707:5)
flutter: #30     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #31     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #32     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #33     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #34     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #35     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #36     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #37     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #38     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #39     Element.updateChildren (package:flutter/src/widgets/framework.dart:4090:32)
flutter: #40     MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #41     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #42     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #43     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #44     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #45     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #46     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #47     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #48     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #49     StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #50     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #51     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #52     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #53     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #54     StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #55     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #56     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #57     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #58     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #59     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #60     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #61     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #62     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #63     StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #64     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #65     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #66     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #67     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #68     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #69     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #70     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #71     SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #72     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #73     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #74     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #75     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #76     StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #77     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #78     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #79     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #80     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #81     StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #82     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #83     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #84     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #85     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #86     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #87     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #88     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #89     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #90     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #91     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #92     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #93     ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #94     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #95     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #96     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #97     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #98     StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #99     Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #100    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #101    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #102    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #103    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #104    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #105    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #106    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #107    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #108    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #109    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #110    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #111    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #112    BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #113    BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #114    BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #115    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1176:21)
flutter: #116    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #117    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #118    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #119    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #120    _invoke (dart:ui/hooks.dart:312:13)
flutter: #121    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #122    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-01T01:10:44.078631] [PerformanceMonitoringService] Slow frame detected {"duration_ms":61}
flutter: 🐛 DEBUG [2025-07-01T01:10:44.104955] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-07-01T01:10:44.484012] [PerformanceMonitoringService] Slow frame detected {"duration_ms":63}
flutter: 🐛 DEBUG [2025-07-01T01:10:44.510726] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-07-01T01:10:45.532257] [PerformanceMonitoringService] Slow frame detected {"duration_ms":61}
flutter: 🐛 DEBUG [2025-07-01T01:10:45.554891] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-07-01T01:10:46.578272] [PerformanceMonitoringService] Slow frame detected {"duration_ms":57}
flutter: 🐛 DEBUG [2025-07-01T01:10:46.604609] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-07-01T01:10:47.626277] [PerformanceMonitoringService] Slow frame detected {"duration_ms":55}
flutter: 🐛 DEBUG [2025-07-01T01:10:47.654685] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-01T01:10:48.672224] [PerformanceMonitoringService] Slow frame detected {"duration_ms":51}
flutter: ⚠️ WARNING [2025-07-01T01:10:49.480334] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: 🐛 DEBUG [2025-07-01T01:10:49.726776] [PerformanceMonitoringService] Slow frame detected {"duration_ms":55}
flutter: 🐛 DEBUG [2025-07-01T01:10:49.754632] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-01T01:10:50.769080] [PerformanceMonitoringService] Slow frame detected {"duration_ms":48}
flutter: 🐛 DEBUG [2025-07-01T01:10:50.788534] [PerformanceMonitoringService] Slow frame detected {"duration_ms":18}
flutter: 🐛 DEBUG [2025-07-01T01:10:51.812590] [PerformanceMonitoringService] Slow frame detected {"duration_ms":58}
flutter: 🐛 DEBUG [2025-07-01T01:10:51.837503] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-01T01:10:52.862513] [PerformanceMonitoringService] Slow frame detected {"duration_ms":58}
flutter: 🐛 DEBUG [2025-07-01T01:10:52.887883] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-01T01:10:53.916287] [PerformanceMonitoringService] Slow frame detected {"duration_ms":61}
flutter: 🐛 DEBUG [2025-07-01T01:10:53.938197] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-01T01:10:54.965774] [PerformanceMonitoringService] Slow frame detected {"duration_ms":62}
flutter: 🐛 DEBUG [2025-07-01T01:10:54.987241] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-07-01T01:10:56.010777] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-01T01:10:56.038251] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-01T01:10:57.065003] [PerformanceMonitoringService] Slow frame detected {"duration_ms":60}
flutter: 🐛 DEBUG [2025-07-01T01:10:57.087990] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-01T01:10:58.113276] [PerformanceMonitoringService] Slow frame detected {"duration_ms":59}
flutter: 🐛 DEBUG [2025-07-01T01:10:58.137255] [PerformanceMonitoringService] Slow frame detected {"duration_ms":23}
flutter: 🐛 DEBUG [2025-07-01T01:10:59.171316] [PerformanceMonitoringService] Slow frame detected {"duration_ms":67}
flutter: 🐛 DEBUG [2025-07-01T01:11:00.243919] [PerformanceMonitoringService] Slow frame detected {"duration_ms":73}
flutter: 🐛 DEBUG [2025-07-01T01:11:00.270645] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-01T01:11:01.302411] [PerformanceMonitoringService] Slow frame detected {"duration_ms":65}
flutter: 🐛 DEBUG [2025-07-01T01:11:01.320512] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: 🐛 DEBUG [2025-07-01T01:11:02.355265] [PerformanceMonitoringService] Slow frame detected {"duration_ms":67}
flutter: 🐛 DEBUG [2025-07-01T01:11:03.389411] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: ⚠️ WARNING [2025-07-01T01:11:04.480172] [PerformanceMonitoringService] High memory usage detected {"memory_mb":180.0}
flutter: ⚠️ WARNING [2025-07-01T01:11:19.480067] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: ⚠️ WARNING [2025-07-01T01:11:34.479637] [PerformanceMonitoringService] High memory usage detected {"memory_mb":179.0}
flutter: ⚠️ WARNING [2025-07-01T01:11:49.479106] [PerformanceMonitoringService] High memory usage detected {"memory_mb":178.0}
flutter: ⚠️ WARNING [2025-07-01T01:12:04.478685] [PerformanceMonitoringService] High memory usage detected {"memory_mb":178.0}


