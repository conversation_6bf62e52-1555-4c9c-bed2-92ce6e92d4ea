flutter: 🐛 DEBUG [2025-06-30T21:34:06.454410] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-06-30T21:34:06.522227] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:34:20.989482] [PerformanceMonitoringService] High memory usage detected {"memory_mb":189.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:34:35.989216] [PerformanceMonitoringService] High memory usage detected {"memory_mb":189.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:34:50.988745] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:35:05.989185] [PerformanceMonitoringService] High memory usage detected {"memory_mb":189.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:35:20.988776] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:35:35.988765] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:35:50.988336] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:36:05.988152] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:36:20.988211] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:36:35.987913] [PerformanceMonitoringService] High memory usage detected {"memory_mb":187.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:36:50.987781] [PerformanceMonitoringService] High memory usage detected {"memory_mb":187.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:37:05.986482] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:37:20.987656] [PerformanceMonitoringService] High memory usage detected {"memory_mb":187.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:37:35.987240] [PerformanceMonitoringService] High memory usage detected {"memory_mb":187.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:37:50.986328] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🐛 DEBUG [2025-06-30T21:38:04.618989] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-06-30T21:38:05.987494] [PerformanceMonitoringService] High memory usage detected {"memory_mb":187.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:38:20.987050] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🐛 DEBUG [2025-06-30T21:38:32.351696] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-06-30T21:38:32.585033] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:38:35.985830] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🐛 DEBUG [2025-06-30T21:38:45.568293] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:38:50.986865] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:39:05.986431] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:39:20.985158] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:39:35.986046] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:39:50.986154] [PerformanceMonitoringService] High memory usage detected {"memory_mb":186.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:40:05.985895] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:40:20.985584] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:40:35.985524] [PerformanceMonitoringService] High memory usage detected {"memory_mb":185.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: ⚠️ WARNING [2025-06-30T21:40:50.985214] [PerformanceMonitoringService] High memory usage detected {"memory_mb":184.0}
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
flutter: 📋 Token claims - email_verified: false
flutter: 📋 User.emailVerified property: false
flutter: ❌ Email not yet verified via token-based check
flutter: 🔍 Starting email verification check...
flutter: ✅ User from currentUserProvider: <EMAIL>
flutter: ✅ User authenticated via currentUserProvider: <EMAIL> (verified: false)
flutter: 🔍 Checking verification via ID token for: <EMAIL>
